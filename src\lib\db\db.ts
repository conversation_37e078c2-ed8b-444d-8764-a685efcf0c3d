import postgres from 'postgres';
import type { Options } from 'postgres';
import { isProd, PUBLIC_SITE_DOMAIN } from '$lib/utils/env';

function getDatabaseName(domain: string): string {
  return domain.replace(/\./g, '_');
}

const connectionConfig: Options<{}> = isProd
  ? {
      host: '/var/run/postgresql',
      database: getDatabaseName(PUBLIC_SITE_DOMAIN),
      username: 'demo',
      password: 'soydemo.com',
      ssl: false,
    }
  : {
      host: '*************',
      port: 5432,
      database: getDatabaseName(PUBLIC_SITE_DOMAIN),
      username: 'demo',
      password: 'soydemo.com',
      ssl: false,
    };

const sql = postgres(connectionConfig);

console.log('connectionConfig: ', connectionConfig);

export default sql;