<script lang="ts">
	import { goto } from '$app/navigation';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card';
	import Head from '$lib/components/Head.svelte';
	import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
	import { authStore } from '$lib/stores/auth';
	import { onMount } from 'svelte';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	let firstname = $state('');
	let email = $state('');
	let password = $state('');
	let confirmPassword = $state('');
	let isLoading = $state(false);
	let error = $state('');

	onMount(() => {
		const unsubscribe = authStore.subscribe(auth => {
			if (auth.isAuthenticated && !auth.loading) {
				goto('/');
			}
		});

		return unsubscribe;
	});

	async function handleSignup() {
		if (!firstname || !email || !password || !confirmPassword) {
			error = 'Please fill in all fields';
			return;
		}

		if (password !== confirmPassword) {
			error = 'Passwords do not match';
			return;
		}

		if (password.length < 6) {
			error = 'Password must be at least 6 characters long';
			return;
		}

		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(email)) {
			error = 'Please enter a valid email address';
			return;
		}

		isLoading = true;
		error = '';

		try {
			const response = await fetch('/api/user-signup', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ firstname, email, password, confirmPassword })
			});

			const result = await response.json();

			if (result.success) {
				authStore.login(result.data.token, result.data.user);
				goto(data.redirectTo);
			} else {
				error = result.error || 'Signup failed';
			}
		} catch (err) {
			console.error('Signup error:', err);
			error = 'Network error. Please try again.';
		} finally {
			isLoading = false;
		}
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			handleSignup();
		}
	}
</script>

<Head
	title="Sign Up - {PUBLIC_SITE_NAME}"
	description="Create your account to submit and manage your AI tools."
	url="{PUBLIC_SITE_URL}/signup"
/>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
	<div class="max-w-md w-full">
		<Card class="bg-white shadow-lg">
			<CardHeader class="space-y-1 py-6">
				<CardTitle class="text-3xl font-bold text-center text-blue-900">Join Us</CardTitle>
				<CardDescription class="text-center text-gray-600">
					Create your account to get started
				</CardDescription>
			</CardHeader>
			<CardContent class="space-y-6 px-6 pb-6">
				<form onsubmit={(e) => { e.preventDefault(); handleSignup(); }} class="space-y-5">
					<div>
						<label for="firstname" class="block text-sm font-medium text-gray-700 mb-1">
							First Name
						</label>
						<Input
							type="text"
							id="firstname"
							bind:value={firstname}
							required
							placeholder="Enter your first name"
							class="w-full"
							onkeypress={handleKeyPress}
						/>
					</div>

					<div>
						<label for="email" class="block text-sm font-medium text-gray-700 mb-1">
							Email Address
						</label>
						<Input
							type="email"
							id="email"
							bind:value={email}
							required
							placeholder="Enter your email"
							class="w-full"
							onkeypress={handleKeyPress}
						/>
					</div>

					<div>
						<label for="password" class="block text-sm font-medium text-gray-700 mb-1">
							Password
						</label>
						<Input
							type="password"
							id="password"
							bind:value={password}
							required
							placeholder="Enter your password (min. 6 characters)"
							class="w-full"
							onkeypress={handleKeyPress}
						/>
					</div>

					<div>
						<label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">
							Confirm Password
						</label>
						<Input
							type="password"
							id="confirmPassword"
							bind:value={confirmPassword}
							required
							placeholder="Confirm your password"
							class="w-full"
							onkeypress={handleKeyPress}
						/>
					</div>

					{#if error}
						<div class="text-red-600 text-sm text-center bg-red-50 p-3 rounded-md">
							{error}
						</div>
					{/if}

					<Button
						type="submit"
						disabled={isLoading || !firstname || !email || !password || !confirmPassword}
						class="w-full btn-primary"
						size="lg"
					>
						{isLoading ? 'Creating Account...' : 'Create Account'}
					</Button>
				</form>

				<div class="text-center text-sm text-gray-600">
					Already have an account?
					<a href="/login" class="text-blue-600 hover:text-blue-800 font-medium">
						Sign in here
					</a>
				</div>
			</CardContent>
		</Card>
	</div>
</div>
