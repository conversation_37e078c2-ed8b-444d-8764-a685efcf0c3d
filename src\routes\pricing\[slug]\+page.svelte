<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import ToolCard from '$lib/components/ToolCard.svelte';
	import Head from '$lib/components/Head.svelte';
	import { ChevronLeft, ChevronRight, DollarSign } from 'lucide-svelte';
	import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	// Helper function to get metadata value
	function getMetaValue(item: any, key: string): string {
		if (!item.metadata) return '';
		const meta = item.metadata.find((m: any) => m.item_meta_key === key);
		return meta ? meta.item_meta_value || '' : '';
	}

	// Helper function to get term color
	function getTermColor(item: any, taxonomy: string): string {
		if (!item.terms) return '#000000';
		const term = item.terms.find((t: any) => t.term_taxonomy === taxonomy);
		return term?.term_color || '#000000';
	}

	// Helper function to get category name
	function getCategoryName(item: any): string {
		if (!item.terms) return '';
		const categoryTerm = item.terms.find((t: any) => t.term_taxonomy === 'category');
		return categoryTerm?.term_name || '';
	}

	// Helper function to get pricing name
	function getPricingName(item: any): string {
		if (!item.terms) return '';
		const pricingTerm = item.terms.find((t: any) => t.term_taxonomy === 'pricing');
		return pricingTerm?.term_name || '';
	}

	// Helper function to get thumbnail URL
	function getThumbnailUrl(item: any): string {
		if (!item.metadata) return '/assets/images/default-screenshot.jpg';
		const meta = item.metadata.find((m: any) => m.item_meta_key === 'thumbnail_url');
		return meta && meta.item_meta_value ? meta.item_meta_value : '/assets/images/default-screenshot.jpg';
	}

	// Helper function to get category slug
	function getCategorySlug(item: any): string {
		if (!item.terms) return '';
		const categoryTerm = item.terms.find((t: any) => t.term_taxonomy === 'category');
		return categoryTerm?.term_slug || '';
	}

	// Helper function to get pricing slug
	function getPricingSlug(item: any): string {
		if (!item.terms) return '';
		const pricingTerm = item.terms.find((t: any) => t.term_taxonomy === 'pricing');
		return pricingTerm?.term_slug || '';
	}

	// Generate pagination links
	function generatePageNumbers(): number[] {
		const pages: number[] = [];
		const start = Math.max(1, data.currentPage - 2);
		const end = Math.min(data.totalPages, data.currentPage + 2);
		
		for (let i = start; i <= end; i++) {
			pages.push(i);
		}
		return pages;
	}
</script>

<Head
	title="{data.pricing.term_name} AI Tools - {PUBLIC_SITE_NAME}"
	description="Discover {data.pricing.term_name.toLowerCase()} AI tools. Find AI solutions that fit your budget."
	url="{PUBLIC_SITE_URL}/pricing/{data.pricing.term_slug}"
/>

<div class="container mx-auto py-12 px-4 sm:px-6">
	<div class="text-center mb-12">
		<h1 class="text-3xl md:text-4xl font-bold text-blue-900 mb-6">
			{data.pricing.term_name} AI Tools
		</h1>
		<p class="text-lg text-gray-600 max-w-2xl mx-auto">
			Discover {data.pricing.term_name.toLowerCase()} AI tools that fit your budget and needs.
		</p>
	</div>

	<!-- Tools Grid -->
	{#if data.items.length > 0}
		<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
			{#each data.items as item}
				<ToolCard
					name={item.item_name}
					description={getMetaValue(item, 'description') || 'AI tool for productivity'}
					category={getCategoryName(item)}
					price={getPricingName(item)}
					imageColor="e0e7ff/3730a3"
					categoryColor={getTermColor(item, 'category')}
					slug={item.item_slug}
					thumbnailUrl={getThumbnailUrl(item)}
					categorySlug={getCategorySlug(item)}
					pricingSlug={getPricingSlug(item)}
				/>
			{/each}
		</div>

		<!-- Pagination -->
		{#if data.totalPages > 1}
			<div class="flex justify-center items-center space-x-2">
				<!-- Previous Button -->
				{#if data.hasPrevPage}
					<a href="/pricing/{data.pricing.term_slug}?page={data.currentPage - 1}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700">
						<ChevronLeft class="w-4 h-4 mr-1" />
						Previous
					</a>
				{:else}
					<span class="flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-lg cursor-not-allowed">
						<ChevronLeft class="w-4 h-4 mr-1" />
						Previous
					</span>
				{/if}

				<!-- Page Numbers -->
				{#each generatePageNumbers() as pageNum}
					{#if pageNum === data.currentPage}
						<span class="px-3 py-2 text-sm font-medium text-white bg-blue-900 border border-blue-900 rounded-lg">
							{pageNum}
						</span>
					{:else}
						<a href="/pricing/{data.pricing.term_slug}?page={pageNum}" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700">
							{pageNum}
						</a>
					{/if}
				{/each}

				<!-- Next Button -->
				{#if data.hasNextPage}
					<a href="/pricing/{data.pricing.term_slug}?page={data.currentPage + 1}" class="flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700">
						Next
						<ChevronRight class="w-4 h-4 ml-1" />
					</a>
				{:else}
					<span class="flex items-center px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-lg cursor-not-allowed">
						Next
						<ChevronRight class="w-4 h-4 ml-1" />
					</span>
				{/if}
			</div>

			<!-- Page Info -->
			<div class="text-center mt-4 text-sm text-gray-600">
				Showing page {data.currentPage} of {data.totalPages} ({data.total} tools total)
			</div>
		{/if}
	{:else}
		<div class="text-center py-12">
			<DollarSign class="w-16 h-16 text-gray-400 mx-auto mb-4" />
			<h3 class="text-xl font-semibold text-gray-600 mb-2">No Tools Found</h3>
			<p class="text-gray-500">No {data.pricing.term_name.toLowerCase()} AI tools found yet.</p>
			<a href="/categories" class="inline-block mt-4 text-blue-600 hover:underline">Browse categories</a>
		</div>
	{/if}
</div>
