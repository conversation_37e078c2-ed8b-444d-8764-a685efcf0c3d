<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';

	import ToolCard from '$lib/components/ToolCard.svelte';
	import FeaturedTools from '$lib/components/FeaturedTools.svelte';
	import Head from '$lib/components/Head.svelte';
	import { Search } from 'lucide-svelte';
	import { PUBLIC_SITE_URL, PUBLIC_SITE_DESCRIPTION, PUBLIC_SITE_NAME, PUBLIC_HERO_HEADING, PUBLIC_HERO_SUBHEADING } from '$lib/utils/env';
	import { goto } from '$app/navigation';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	// Search input state
	let searchQuery = $state('');

	// Helper function to get metadata value
	function getMetaValue(item: any, key: string): string {
		if (!item.metadata) return '';
		const meta = item.metadata.find((m: any) => m.item_meta_key === key);
		return meta ? meta.item_meta_value || '' : '';
	}

	// Helper function to get term color (default for now)
	function getTermColor(item: any, taxonomy: string): string {
		// For now return default color, will enhance with term_color metadata later
		return '#3730a3';
	}

	// Handle search form submission
	function handleSearch(event: Event) {
		event.preventDefault();
		if (searchQuery.trim()) {
			goto(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
		}
	}

	// Helper function to get category name
	function getCategoryName(item: any): string {
		if (!item.terms) return '';
		const categoryTerm = item.terms.find((t: any) => t.term_taxonomy === 'category');
		return categoryTerm?.term_name || '';
	}

	// Helper function to get pricing name
	function getPricingName(item: any): string {
		if (!item.terms) return '';
		const pricingTerm = item.terms.find((t: any) => t.term_taxonomy === 'pricing');
		return pricingTerm?.term_name || '';
	}

	// Helper function to get category slug
	function getCategorySlug(item: any): string {
		if (!item.terms) return '';
		const categoryTerm = item.terms.find((t: any) => t.term_taxonomy === 'category');
		return categoryTerm?.term_slug || '';
	}

	// Helper function to get pricing slug
	function getPricingSlug(item: any): string {
		if (!item.terms) return '';
		const pricingTerm = item.terms.find((t: any) => t.term_taxonomy === 'pricing');
		return pricingTerm?.term_slug || '';
	}

	// Helper function to get thumbnail URL
	function getThumbnailUrl(item: any): string {
		if (!item.metadata) return '/assets/images/default-screenshot.jpg';
		const meta = item.metadata.find((m: any) => m.item_meta_key === 'thumbnail_url');
		return meta && meta.item_meta_value ? meta.item_meta_value : '/assets/images/default-screenshot.jpg';
	}

	// Helper function to get category description
	function getCategoryDescription(category: any): string {
		if (!category.metadata) return '';
		const meta = category.metadata.find((m: any) => m.term_meta_key === 'description');
		return meta ? meta.term_meta_value || '' : '';
	}


</script>

<Head
	title="Best AI Tools Directory - {PUBLIC_SITE_NAME}"
	description={PUBLIC_SITE_DESCRIPTION}
	url={PUBLIC_SITE_URL}
/>

<!-- Hero Section -->
<section class="bg-blue-900 text-white py-20 lg:py-28 px-4 sm:px-6 relative overflow-hidden" style="background-image: radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.1) 1px, transparent 0); background-size: 20px 20px;">
	<div class="container mx-auto text-center relative z-10">
		<h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-4 leading-tight">
			{PUBLIC_HERO_HEADING}
		</h1>
		<p class="text-lg md:text-xl text-gray-300 mb-8 max-w-3xl mx-auto opacity-90">
			{PUBLIC_HERO_SUBHEADING}
		</p>
		<div class="max-w-2xl mx-auto relative">
			<form onsubmit={handleSearch}>
				<Input
					type="text"
					bind:value={searchQuery}
					placeholder="Search for a tool, e.g., 'image generator'..."
					class="w-full h-auto py-4 pl-12 pr-4 rounded-full text-gray-800 bg-white shadow-md focus:border-blue-900 focus:ring-2 focus:ring-blue-900/30 transition-all duration-300"
				/>
				<Search class="absolute left-5 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5" />
			</form>
		</div>
	</div>
</section>

<!-- Main Directory Section -->
<section class="py-16 lg:py-24">
	<div class="container mx-auto px-4 sm:px-6">
		<h2 class="text-3xl md:text-4xl font-bold mb-8 text-blue-900">Explore All AI Tools</h2>

		<!-- Filter Bar -->
		<div class="mb-10 flex flex-wrap gap-4">
			<Button href="/new" variant="outline" class="px-4 py-2 text-sm font-medium border border-gray-300">Newest</Button>
			<Button href="/featured" variant="outline" class="px-4 py-2 text-sm font-medium border border-gray-300">Featured</Button>
		</div>

		<div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
			<!-- Main Tools Grid -->
			<div class="lg:col-span-9">
				<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
					{#each data.items as item}
						<ToolCard
							name={item.item_name}
							description={getMetaValue(item, 'description') || 'AI tool for productivity'}
							category={getCategoryName(item)}
							price={getPricingName(item)}
							imageColor="e0e7ff/3730a3"
							categoryColor={getTermColor(item, 'category')}
							slug={item.item_slug}
							thumbnailUrl={getThumbnailUrl(item)}
							categorySlug={getCategorySlug(item)}
							pricingSlug={getPricingSlug(item)}
						/>
					{/each}
				</div>

				<div class="flex justify-center mt-10">
					<Button href="/new" class="btn-secondary">
						More Tools
					</Button>
				</div>
			</div>

			<!-- Featured Tools Sidebar -->
			<aside class="lg:col-span-3">
				<FeaturedTools items={data.featuredItems} />
			</aside>
		</div>
	</div>
</section>

<!-- Categories Section -->
<section id="categories" class="py-16 lg:py-20 bg-white border-t border-gray-100">
	<div class="container mx-auto px-4 sm:px-6">
		<h2 class="text-3xl font-bold mb-10 text-center text-blue-900">Or, Explore by Category</h2>
		<div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 md:gap-6">
			{#each data.categories as category}
				<a href="/category/{category.term_slug}" class="group block text-center bg-gray-50 p-3 rounded-xl border border-gray-200 hover:border-blue-900 hover:shadow-md hover:-translate-y-1 transition-all duration-300 ease-in-out">
					<div class="flex flex-col items-center">
						<h3 class="font-semibold text-blue-900 mb-2">{category.term_name}</h3>
						{#if getCategoryDescription(category)}
							<p class="text-xs text-gray-600 text-center">{getCategoryDescription(category)}</p>
						{/if}
					</div>
				</a>
			{/each}
		</div>
	</div>
</section>

<!-- Submit Your Tool Section -->
<section id="submit-tool" class="bg-blue-900 py-20 lg:py-24 text-white text-center">
	<div class="container mx-auto px-6">
		<h2 class="text-4xl md:text-5xl font-bold mb-3 leading-tight">Have an AI Tool?</h2>
		<p class="text-xl text-gray-200 mb-10 font-medium max-w-2xl mx-auto">Get featured in our directory and reach thousands of users.</p>
		<ul class="space-y-4 text-lg text-gray-100 inline-block text-left mx-auto">
			<li class="flex items-center">
				<svg class="w-6 h-6 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
				</svg>
				<span>Reach thousands of AI enthusiasts</span>
			</li>
			<li class="flex items-center">
				<svg class="w-6 h-6 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
				</svg>
				<span>Get valuable user feedback</span>
			</li>
			<li class="flex items-center">
				<svg class="w-6 h-6 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
				</svg>
				<span>Boost your tool's visibility</span>
			</li>
		</ul>
		<div class="mt-12">
			<Button href="/submit" class="btn-secondary" size="xl">
				Submit Your Tool
			</Button>
		</div>
	</div>
</section>
