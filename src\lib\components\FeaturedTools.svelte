<script lang="ts">
	import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>er, CardTitle } from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { Star, ExternalLink } from 'lucide-svelte';

	interface FeaturedToolsProps {
		items: any[];
	}

	let { items }: FeaturedToolsProps = $props();

	// Helper function to get metadata value
	function getMetaValue(item: any, key: string): string {
		if (!item.metadata) return '';
		const meta = item.metadata.find((m: any) => m.item_meta_key === key);
		return meta ? meta.item_meta_value || '' : '';
	}

	// Helper function to get icon URL
	function getIconUrl(item: any): string {
		const iconUrl = getMetaValue(item, 'icon_url');
		if (iconUrl) return iconUrl;

		// Fallback to default icon
		return '/assets/images/default-icon.jpg';
	}
</script>

<Card class="bg-white p-6 rounded-sm border border-gray-200 shadow-sm sticky top-28">
	<CardHeader class="p-0 mb-4">
		<CardTitle class="font-bold text-xl text-blue-900 flex items-center">
			<Star class="text-orange-500 mr-2 w-5 h-5 fill-current" />
			Featured Tools
		</CardTitle>
	</CardHeader>
	
	<CardContent class="p-0">
		<div class="flex flex-col gap-2 space-y-4">
			{#each items as item}
				<a
					href="/ai/{item.item_slug}"
					class="group flex items-center justify-between"
				>
					<div class="flex items-start gap-4">
						<img
							src={getIconUrl(item)}
							alt="{item.item_name} logo"
							class="w-12 h-12 rounded-full flex-shrink-0 shadow-sm"
							loading="lazy"
							decoding="async"
						>
						<div>
							<div class="flex justify-between">
								<p class="font-semibold text-blue-900 group-hover:underline">{item.item_name}</p>
								<ExternalLink class="w-5 h-5 text-gray-400 group-hover:text-blue-900 transition-colors" />
							</div>
							<p class="text-xs text-gray-500">{getMetaValue(item, 'description') || 'AI tool for productivity'}</p>
						</div>
					</div>
				</a>
			{/each}
		</div>
		<div class="flex justify-center mt-10">
			<Button href="/new" class="btn-secondary">
				More Featured Tools
			</Button>
		</div>
	</CardContent>
</Card>
