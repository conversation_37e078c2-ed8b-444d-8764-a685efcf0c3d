<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import {
		Dialog,
		DialogContent,
		DialogDescription,
		DialogFooter,
		DialogHeader,
		DialogTitle,
		DialogTrigger
	} from '$lib/components/ui/dialog';
	import Head from '$lib/components/Head.svelte';
	import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
	import type { PageData } from './$types';

	import { Info, Check, Lightbulb, AlertTriangle, Edit, Image, ChevronDown } from 'lucide-svelte';

	let { data }: { data: PageData } = $props();

	// Form state
	let selectedCategories = $state<string[]>([]);
	let selectedPricing = $state('');
	let websiteUrl = $state('');
	let toolName = $state('');
	let shortDescription = $state('');
	let aboutTool = $state('');
	let screenshotFile: File | null = $state(null);
	let iconFile: File | null = $state(null);
	let screenshotPreview: string | null = $state(null);
	let iconPreview: string | null = $state(null);
	let agreeToTerms = $state(false);
	let isSubmitting = $state(false);
	let isUploadingScreenshot = $state(false);
	let isUploadingIcon = $state(false);

	// Modal state
	let categoriesModalOpen = $state(false);
	let pricingModalOpen = $state(false);

	// Form submission handler
	async function handleSubmit() {
		// Validate required fields
		if (!websiteUrl || !toolName || !shortDescription || !aboutTool || selectedCategories.length === 0 || !selectedPricing) {
			alert('Please fill in all required fields');
			return;
		}

		// Validate URLs
		try {
			new URL(websiteUrl);
		} catch {
			alert('Please enter a valid website URL');
			return;
		}

		// Validate file sizes
		if (screenshotFile && screenshotFile.size > 2 * 1024 * 1024) {
			alert('Screenshot file must be less than 2MB');
			return;
		}

		if (!agreeToTerms) {
			alert('Please agree to the terms and conditions');
			return;
		}

		isSubmitting = true;

		try {
			const formData = new FormData();
			formData.append('websiteUrl', websiteUrl);
			formData.append('toolName', toolName);
			formData.append('shortDescription', shortDescription);
			formData.append('aboutTool', aboutTool);
			formData.append('selectedCategories', JSON.stringify(selectedCategories));
			formData.append('selectedPricing', selectedPricing);

			if (screenshotFile) {
				formData.append('screenshotFile', screenshotFile);
			}

			if (iconFile) {
				formData.append('iconFile', iconFile);
			}

			const response = await fetch('/api/submit-tool', {
				method: 'POST',
				body: formData
			});

			const result = await response.json();

			if (response.ok) {
				alert('Tool submitted successfully! It will be reviewed before being published.');
				// Reset form
				websiteUrl = '';
				toolName = '';
				shortDescription = '';
				aboutTool = '';
				selectedCategories = [];
				selectedPricing = '';
				screenshotFile = null;
				iconFile = null;
				agreeToTerms = false;
			} else {
				alert(result.error || 'Error submitting tool');
			}
		} catch (error) {
			console.error('Error:', error);
			alert('Error submitting tool');
		} finally {
			isSubmitting = false;
		}
	}



	// Upload image to server and get preview
	async function uploadImage(file: File, type: 'screenshot' | 'icon') {
		const formData = new FormData();
		formData.append('file', file);
		formData.append('type', type);

		try {
			const response = await fetch('/api/upload-image', {
				method: 'POST',
				body: formData
			});

			const result = await response.json();

			if (response.ok) {
				return result.imageUrl;
			} else {
				alert(result.error || 'Failed to upload image');
				return null;
			}
		} catch (error) {
			console.error('Upload error:', error);
			alert('Failed to upload image');
			return null;
		}
	}

	// Handle file uploads
	async function handleScreenshotUpload(event: Event) {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files[0]) {
			const file = target.files[0];
			screenshotFile = file;
			isUploadingScreenshot = true;

			const imageUrl = await uploadImage(file, 'screenshot');
			if (imageUrl) {
				screenshotPreview = imageUrl;
			}

			isUploadingScreenshot = false;
		}
	}

	async function handleIconUpload(event: Event) {
		const target = event.target as HTMLInputElement;
		if (target.files && target.files[0]) {
			const file = target.files[0];
			iconFile = file;
			isUploadingIcon = true;

			const imageUrl = await uploadImage(file, 'icon');
			if (imageUrl) {
				iconPreview = imageUrl;
			}

			isUploadingIcon = false;
		}
	}

	// Handle drag and drop for screenshot
	async function handleScreenshotDrop(event: DragEvent) {
		event.preventDefault();
		const files = event.dataTransfer?.files;
		if (files && files[0]) {
			const file = files[0];
			screenshotFile = file;
			isUploadingScreenshot = true;

			const imageUrl = await uploadImage(file, 'screenshot');
			if (imageUrl) {
				screenshotPreview = imageUrl;
			}

			isUploadingScreenshot = false;
		}
	}

	// Handle drag and drop for icon
	async function handleIconDrop(event: DragEvent) {
		event.preventDefault();
		const files = event.dataTransfer?.files;
		if (files && files[0]) {
			const file = files[0];
			iconFile = file;
			isUploadingIcon = true;

			const imageUrl = await uploadImage(file, 'icon');
			if (imageUrl) {
				iconPreview = imageUrl;
			}

			isUploadingIcon = false;
		}
	}

	function handleDragOver(event: DragEvent) {
		event.preventDefault();
	}

	// Handle click on upload area
	function triggerScreenshotUpload() {
		const input = document.getElementById('screenshot-upload') as HTMLInputElement;
		input?.click();
	}

	function triggerIconUpload() {
		const input = document.getElementById('icon-upload') as HTMLInputElement;
		input?.click();
	}
</script>

<Head
	title="Submit a Tool - {PUBLIC_SITE_NAME}"
	description="Submit your AI tool to our directory and reach thousands of users."
	url="{PUBLIC_SITE_URL}/submit"
/>

<div class="container mx-auto py-12 px-4 sm:px-6">
	<div class="mb-6">
		<h1 class="text-3xl md:text-4xl font-bold text-blue-900 mb-2">Submit Your AI Tool</h1>
		<p class="text-gray-600">Welcome back, {data.user.user_firstname}! Submit your AI tool below.</p>
	</div>

	<div class="grid grid-cols-1 lg:grid-cols-12 gap-12">
		<!-- Left Column: Form -->
		<div class="lg:col-span-8">
			<Card class="bg-white p-8 rounded-xl border border-gray-200 shadow-md">
				<form onsubmit={(e) => { e.preventDefault(); handleSubmit(); }} class="space-y-10">
					<!-- Tool Information -->
					<div class="space-y-5">
						<h2 class="text-2xl font-bold text-primary-blue border-b border-gray-200 pb-4">Tool Information</h2>
						<div>
							<label for="website-url" class="block text-sm font-medium text-gray-700 mb-1">
								Website URL <span class="text-red-500">*</span>
							</label>
							<Input
								type="url"
								id="website-url"
								bind:value={websiteUrl}
								required
								class="focus-ring-primary-blue"
								placeholder="https://your-tool.com"
							/>
							<p class="text-xs text-gray-500 mt-2">Enter the official website of your AI tool.</p>
						</div>
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<label for="screenshot-upload" class="block text-sm font-medium text-gray-700 mb-1">
									Upload a screenshot (max. 1500x900) <span class="text-red-500">*</span>
								</label>
								<div
									class="mt-1 relative border-2 border-gray-300 border-dashed rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-200 cursor-pointer"
									ondrop={handleScreenshotDrop}
									ondragover={handleDragOver}
									onclick={triggerScreenshotUpload}
									role="button"
									tabindex="0"
									onkeydown={(e) => e.key === 'Enter' && triggerScreenshotUpload()}
								>
									{#if screenshotPreview}
										<div class="relative">
											<img
												src={screenshotPreview}
												alt="Screenshot preview of {toolName || 'your tool'}"
												class="w-full h-48 object-cover rounded-lg"
												loading="lazy"
												decoding="async"
											/>
											<div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
												<p class="text-white text-sm font-medium">Click to change image</p>
											</div>
											{#if isUploadingScreenshot}
												<div class="absolute inset-0 bg-white bg-opacity-75 rounded-lg flex items-center justify-center">
													<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-900"></div>
												</div>
											{/if}
										</div>
									{:else}
										<div class="flex justify-center px-6 pt-10 pb-12">
											<div class="space-y-1 text-center">
												{#if isUploadingScreenshot}
													<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-900 mx-auto"></div>
													<p class="text-sm text-gray-600">Uploading...</p>
												{:else}
													<Image class="mx-auto h-12 w-12 text-gray-400" />
													<div class="text-sm text-gray-600">
														<span class="font-medium text-blue-900 hover:text-orange-500">Upload a file</span>
														<span class="pl-1">or drag and drop</span>
													</div>
													<p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
												{/if}
											</div>
										</div>
									{/if}
									<input
										id="screenshot-upload"
										type="file"
										class="sr-only"
										accept="image/*"
										onchange={handleScreenshotUpload}
									/>
								</div>
							</div>

							<div>
								<label for="icon-upload" class="block text-sm font-medium text-gray-700 mb-1">
									Upload an icon <span class="text-red-500">*</span>
								</label>
								<div
									class="mt-1 relative border-2 border-gray-300 border-dashed rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-200 cursor-pointer"
									ondrop={handleIconDrop}
									ondragover={handleDragOver}
									onclick={triggerIconUpload}
									role="button"
									tabindex="0"
									onkeydown={(e) => e.key === 'Enter' && triggerIconUpload()}
								>
									{#if iconPreview}
										<div class="relative">
											<img
												src={iconPreview}
												alt="Icon preview of {toolName || 'your tool'}"
												class="w-full h-48 object-contain rounded-lg bg-white p-4"
												loading="lazy"
												decoding="async"
											/>
											<div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
												<p class="text-white text-sm font-medium">Click to change image</p>
											</div>
											{#if isUploadingIcon}
												<div class="absolute inset-0 bg-white bg-opacity-75 rounded-lg flex items-center justify-center">
													<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-900"></div>
												</div>
											{/if}
										</div>
									{:else}
										<div class="flex justify-center px-6 pt-10 pb-12">
											<div class="space-y-1 text-center">
												{#if isUploadingIcon}
													<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-900 mx-auto"></div>
													<p class="text-sm text-gray-600">Uploading...</p>
												{:else}
													<Image class="mx-auto h-12 w-12 text-gray-400" />
													<div class="text-sm text-gray-600">
														<span class="font-medium text-blue-900 hover:text-orange-500">Upload a file</span>
														<span class="pl-1">or drag and drop</span>
													</div>
													<p class="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
												{/if}
											</div>
										</div>
									{/if}
									<input
										id="icon-upload"
										type="file"
										class="sr-only"
										accept="image/*"
										onchange={handleIconUpload}
									/>
								</div>
							</div>
						</div>

						<!-- Tool Name -->
						<div>
							<label for="tool-name" class="block text-sm font-medium text-gray-700 mb-1">
								Tool Name <span class="text-red-500">*</span>
							</label>
							<Input
								type="text"
								id="tool-name"
								bind:value={toolName}
								required
								class="focus-ring-primary-blue"
								placeholder="e.g., AI Writer Pro"
							/>
						</div>

						<!-- Category and Platform Type -->
						<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
							<div>
								<div class="block text-sm font-medium text-gray-700 mb-2">
									Categories <span class="text-red-500">*</span>
								</div>
								<Dialog bind:open={categoriesModalOpen}>
									<DialogTrigger type="button" class="inline-flex items-center justify-between w-full h-9 px-3 py-1 text-base md:text-sm border border-input bg-background rounded-md shadow-xs outline-none transition-[color,box-shadow] focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50" aria-label="Select categories">
										{selectedCategories.length === 0 ? 'Select categories' : `${selectedCategories.length} selected`}
										<ChevronDown class="h-4 w-4 opacity-50" />
									</DialogTrigger>
									<DialogContent class="sm:max-w-md">
										<DialogHeader>
											<DialogTitle>Select Categories</DialogTitle>
											<DialogDescription>
												Choose one or more categories that best describe your AI tool.
											</DialogDescription>
										</DialogHeader>
										<div class="grid gap-4 py-4 max-h-96 overflow-y-auto">
											{#each data.categories as category}
												<div class="flex items-center space-x-2">
													<Checkbox
														id="category-{category.term_slug}"
														checked={selectedCategories.includes(category.term_slug)}
														onCheckedChange={(checked) => {
															if (checked) {
																selectedCategories = [...selectedCategories, category.term_slug];
															} else {
																selectedCategories = selectedCategories.filter(c => c !== category.term_slug);
															}
														}}
													/>
													<label for="category-{category.term_slug}" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
														{category.term_name}
													</label>
												</div>
											{/each}
										</div>
										<DialogFooter>
											<Button onclick={() => categoriesModalOpen = false} class="btn-primary">Done</Button>
										</DialogFooter>
									</DialogContent>
								</Dialog>
							</div>
							<div>
								<div class="block text-sm font-medium text-gray-700 mb-2">
									Pricing <span class="text-red-500">*</span>
								</div>
								<Dialog bind:open={pricingModalOpen}>
									<DialogTrigger type="button" class="inline-flex items-center justify-between w-full h-9 px-3 py-1 text-base md:text-sm border border-input bg-background rounded-md shadow-xs outline-none transition-[color,box-shadow] focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50" aria-label="Select pricing model">
										{selectedPricing ? data.pricingOptions.find(p => p.term_slug === selectedPricing)?.term_name || 'Select pricing model' : 'Select pricing model'}
										<ChevronDown class="h-4 w-4 opacity-50" />
									</DialogTrigger>
									<DialogContent class="sm:max-w-md">
										<DialogHeader>
											<DialogTitle>Select Pricing Model</DialogTitle>
											<DialogDescription>
												Choose the pricing model that best describes your AI tool.
											</DialogDescription>
										</DialogHeader>
										<div class="grid gap-4 py-4">
											{#each data.pricingOptions as pricing}
												<div class="flex items-center space-x-2">
													<input
														type="radio"
														id="pricing-{pricing.term_slug}"
														name="pricing"
														value={pricing.term_slug}
														bind:group={selectedPricing}
														class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
													/>
													<label for="pricing-{pricing.term_slug}" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
														{pricing.term_name}
													</label>
												</div>
											{/each}
										</div>
										<DialogFooter>
											<Button onclick={() => pricingModalOpen = false} class="btn-primary">Done</Button>
										</DialogFooter>
									</DialogContent>
								</Dialog>
							</div>
						</div>

						<!-- Short Description -->
						<div>
							<label for="summary" class="block text-sm font-medium text-gray-700 mb-1">
								Short Description <span class="text-red-500">*</span>
							</label>
							<Input
								type="text"
								id="summary"
								bind:value={shortDescription}
								required
								maxlength={150}
								class="focus-ring-primary-blue"
								placeholder="Provide a short, impactful description."
							/>
							<p class="text-xs text-gray-500 mt-1">A concise summary of your tool (max 150 characters). {shortDescription.length}/150</p>
						</div>

						<!-- About Your Tool -->
						<div>
							<label for="about-tool" class="block text-sm font-medium text-gray-700 mb-1">
								About Your Tool <span class="text-red-500">*</span>
							</label>
							<Textarea
								id="about-tool"
								bind:value={aboutTool}
								required
								class="focus-ring-primary-blue h-[150px]"
								placeholder="Write an overview of your tool."
							/>
							<p class="text-xs text-gray-500 mt-1">Describe what your tool does, its main purpose, and target audience.</p>
						</div>
					</div>

					<!-- Submission -->
					<div class="pt-6 border-t border-gray-200">
						<div class="flex items-start">
							<Checkbox id="terms" bind:checked={agreeToTerms} required class="mt-1" />
							<div class="ml-3 text-sm">
								<label for="terms" class="font-medium text-gray-700">
									I agree to the <a href="/terms" class="text-primary-blue hover:underline">Terms and Conditions</a>
								</label>
							</div>
						</div>
						<div class="mt-6">
							<Button type="submit" disabled={isSubmitting} size="lg" class="w-full sm:w-auto btn-secondary">
								{isSubmitting ? 'Submitting...' : 'Submit Tool'}
							</Button>
						</div>
					</div>
				</form>
			</Card>
		</div>

		<!-- Right Column: Guidelines -->
		<aside class="lg:col-span-4">
			<Card class="sticky top-28 bg-white p-6 rounded-xl border border-gray-200 shadow-sm">
				<CardHeader class="p-0 mb-4">
					<CardTitle class="text-xl font-bold text-primary-blue flex items-center">
						<Info class="text-orange-highlight mr-2 w-5 h-5" />
						Submission Guidelines
					</CardTitle>
				</CardHeader>
				<CardContent class="p-0">
					<ul class="space-y-3 text-gray-700 text-sm">
						<li class="flex items-start">
							<Check class="text-green-500 mr-2 mt-1 flex-shrink-0 w-4 h-4" />
							<span>Before submitting, please check our directory to ensure your tool hasn't been listed already.</span>
						</li>
						<li class="flex items-start">
							<Image class="text-purple-500 mr-2 mt-1 flex-shrink-0 w-4 h-4" />
							<span>Provide a high-quality screenshot (1500x900 recommended) showcasing your tool's main interface or dashboard.</span>
						</li>
						<li class="flex items-start">
							<Lightbulb class="text-orange-highlight mr-2 mt-1 flex-shrink-0 w-4 h-4" />
							<span>Write clear, compelling descriptions that highlight your tool's unique features and benefits.</span>
						</li>
						<li class="flex items-start">
							<AlertTriangle class="text-yellow-500 mr-2 mt-1 flex-shrink-0 w-4 h-4" />
							<span>Ensure your website is live and accessible before submitting. Broken links will delay approval.</span>
						</li>
						<li class="flex items-start">
							<Check class="text-green-500 mr-2 mt-1 flex-shrink-0 w-4 h-4" />
							<span>Select the most relevant categories and pricing model to help users discover your tool.</span>
						</li>
						<li class="flex items-start">
							<Edit class="text-primary-blue mr-2 mt-1 flex-shrink-0 w-4 h-4" />
							<span>All submissions are reviewed manually. Approval typically takes 1-3 business days.</span>
						</li>
						<li class="flex items-start">
							<AlertTriangle class="text-yellow-500 mr-2 mt-1 flex-shrink-0 w-4 h-4" />
							<span>Need to make changes after submitting? Contact us via our official social channels for assistance.</span>
						</li>
					</ul>
				</CardContent>
			</Card>
		</aside>
	</div>
</div>
