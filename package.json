{"name": "clones", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint ."}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@internationalized/date": "^3.8.1", "@lucide/svelte": "^0.513.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@types/bcrypt": "^5.0.2", "@types/uuid": "^10.0.0", "bits-ui": "^2.5.0", "clsx": "^2.1.1", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "svelte": "^5.0.0", "svelte-adapter-bun": "^0.5.2", "svelte-check": "^4.0.0", "tailwind-merge": "^3.0.2", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.0.0", "tw-animate-css": "^1.3.4", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6"}, "dependencies": {"@types/better-sqlite3": "^7.6.13", "@types/jsonwebtoken": "^9.0.9", "bcrypt": "^6.0.0", "better-sqlite3": "^11.10.0", "google-auth-library": "^10.1.0", "jimp": "^1.6.0", "jsonwebtoken": "^9.0.2", "lucide-svelte": "^0.513.0", "postgres": "^3.4.7", "sql.js": "^1.13.0", "uuid": "^11.1.0"}}