<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Menu, X, LogOut } from 'lucide-svelte';
	import { authStore } from '$lib/stores/auth';
	import { onMount } from 'svelte';
	import { PUBLIC_SITE_NAME } from '$lib/utils/env';

	let mobileMenuOpen = $state(false);
	let authState = $state({ isAuthenticated: false, user: null as any, loading: true });

	onMount(() => {
		const unsubscribe = authStore.subscribe(auth => {
			authState = auth;
		});

		return unsubscribe;
	});

	function toggleMobileMenu() {
		mobileMenuOpen = !mobileMenuOpen;
	}

	function handleLogout() {
		authStore.logout();
		mobileMenuOpen = false;
	}
</script>

<header class="bg-white py-4 shadow-sm sticky top-0 z-50 border-b border-gray-100">
	<div class="container mx-auto px-4 sm:px-6 flex justify-between items-center">
		<!-- Logo -->
		<a href="/" class="flex items-center space-x-3 group">
			<img 
				src="/assets/images/logo.svg" 
				alt="{PUBLIC_SITE_NAME} logo" 
				class="h-9 w-9 rounded-lg shadow-sm group-hover:scale-105 transition-transform"
				width="40"
				height="40"
				decoding="async"
			>
			<span class="text-xl font-bold text-blue-900">{PUBLIC_SITE_NAME}</span>
		</a>
		
		<!-- Desktop Navigation -->
		<nav class="hidden md:flex items-center space-x-7">
			<a href="/new" class="text-gray-600 hover:text-blue-900 text-sm font-medium transition-colors duration-200">
				Discover
			</a>
			<a href="/categories" class="text-gray-600 hover:text-blue-900 text-sm font-medium transition-colors duration-200">
				Categories
			</a>
		</nav>
		
		<!-- Desktop Auth Buttons -->
		<div class="hidden md:flex items-center space-x-4">
			{#if !authState.loading}
				{#if authState.isAuthenticated}
					<span class="text-sm text-gray-600">
						Welcome, {authState.user?.user_firstname}
					</span>
					<Button href="/submit" size="default" class="btn-primary">
						Submit a Tool
					</Button>
					<Button onclick={handleLogout} variant="outline" size="default">
						<LogOut class="w-4 h-4 mr-2" />
						Logout
					</Button>
				{:else}
					<Button href="/login" variant="outline" size="default">
						Login
					</Button>
					<Button href="/signup" size="default" class="btn-primary">
						Sign Up
					</Button>
				{/if}
			{/if}
		</div>

		<!-- Mobile Menu Button -->
		<div class="md:hidden">
			<Button
				onclick={toggleMobileMenu}
				variant="ghost"
				size="icon"
				aria-label="Open menu"
			>
				{#if mobileMenuOpen}
					<X class="h-6 w-6" />
				{:else}
					<Menu class="h-6 w-6" />
				{/if}
			</Button>
		</div>
	</div>
	
	<!-- Mobile Menu -->
	{#if mobileMenuOpen}
		<div class="md:hidden mt-4 bg-white rounded-lg shadow-sm py-2 px-4 border border-gray-100">
			<a href="/categories" class="block py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors">
				Categories
			</a>
			{#if !authState.loading}
				{#if authState.isAuthenticated}
					<div class="block py-2 px-4 text-sm text-gray-600 border-b border-gray-200 mb-2">
						Welcome, {authState.user?.user_firstname}
					</div>
					<a href="/submit" class="block py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors">
						Submit a Tool
					</a>
					<button onclick={handleLogout} class="block w-full text-left py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors">
						<LogOut class="w-4 h-4 mr-2 inline" />
						Logout
					</button>
				{:else}
					<a href="/login" class="block py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors">
						Login
					</a>
					<a href="/signup" class="block py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors">
						Sign Up
					</a>
				{/if}
			{/if}
		</div>
	{/if}
</header>
