<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Card, CardContent } from '$lib/components/ui/card';
	import Head from '$lib/components/Head.svelte';
	import { PUBLIC_SITE_URL } from '$lib/utils/env';
	import { Home, Search, ArrowLeft } from 'lucide-svelte';

	// Popular pages for suggestions
	const popularPages = [
		{ name: 'Browse AI Tools', href: '/', icon: Home },
		{ name: 'Categories', href: '/categories', icon: Search },
		{ name: 'Featured Tools', href: '/featured', icon: '⭐' },
		{ name: 'Submit a Tool', href: '/submit', icon: '📝' }
	];

	function goBack() {
		if (typeof window !== 'undefined') {
			window.history.back();
		}
	}
</script>

<Head
	title="Page Not Found - AI Tools Directory"
	description="The page you're looking for doesn't exist or has been moved. Browse our AI tools directory instead."
	url="{PUBLIC_SITE_URL}/404"
/>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4 sm:px-6">
	<div class="max-w-2xl w-full text-center">
		<!-- Error Icon and Status -->
		<div class="mb-8">
			<div class="text-4xl font-bold text-blue-900 mb-4">404</div>
		</div>

		<!-- Error Message -->
		<Card class="bg-white/80 backdrop-blur-sm border border-white/20 shadow-xl mb-8">
			<CardContent class="p-8">
				<h1 class="text-3xl md:text-4xl font-bold text-blue-900 mb-4">
					Page Not Found
				</h1>
				<p class="text-lg text-gray-600 mb-6 leading-relaxed">
					The page you're looking for doesn't exist or has been moved.
				</p>

				<!-- Action Buttons -->
				<div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
					<Button onclick={goBack} variant="outline" class="flex items-center gap-2">
						<ArrowLeft class="w-4 h-4" />
						Go Back
					</Button>
					<Button href="/" class="btn-primary flex items-center gap-2">
						<Home class="w-4 h-4" />
						Go Home
					</Button>
				</div>
			</CardContent>
		</Card>

		<!-- Popular Pages Suggestions -->
		<div class="text-left">
			<h2 class="text-xl font-semibold text-blue-900 mb-4 text-center">
				Try these popular pages instead:
			</h2>
			<div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
				{#each popularPages as page}
					<a 
						href={page.href}
						class="group bg-white/60 backdrop-blur-sm border border-white/30 rounded-lg p-4 hover:bg-white/80 hover:shadow-md transition-all duration-200 flex items-center gap-3"
					>
						{#if typeof page.icon === 'string'}
							<span class="text-2xl">{page.icon}</span>
						{:else}
							{@const IconComponent = page.icon}
						<IconComponent class="w-6 h-6 text-blue-600" />
						{/if}
						<span class="font-medium text-gray-700 group-hover:text-blue-900 transition-colors">
							{page.name}
						</span>
					</a>
				{/each}
			</div>
		</div>

		<!-- Additional Help -->
		<div class="mt-8 text-center">
			<p class="text-sm text-gray-500 mb-2">
				Still can't find what you're looking for?
			</p>
			<div class="flex flex-col sm:flex-row gap-2 justify-center items-center text-sm">
				<a href="/categories" class="text-blue-600 hover:underline">Browse all categories</a>
				<span class="hidden sm:inline text-gray-400">•</span>
				<a href="/new" class="text-blue-600 hover:underline">Check newest tools</a>
				<span class="hidden sm:inline text-gray-400">•</span>
				<a href="/submit" class="text-blue-600 hover:underline">Submit a tool</a>
			</div>
		</div>
	</div>
</div>
