<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import Head from '$lib/components/Head.svelte';
	import { Check, X, ExternalLink } from 'lucide-svelte';
	import { PUBLIC_SITE_URL } from '$lib/utils/env';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	// Helper function to get metadata value
	function getMetaValue(item: any, key: string): string {
		if (!item.metadata) return '';
		const meta = item.metadata.find((m: any) => m.item_meta_key === key);
		return meta ? meta.item_meta_value || '' : '';
	}

	// Handle approval/rejection
	async function handleItemAction(itemId: number, action: 'approve' | 'reject') {
		try {
			const response = await fetch('/api/manage-item', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					itemId,
					action
				})
			});

			const result = await response.json();

			if (response.ok) {
				// Reload the page to refresh the list
				window.location.reload();
			} else {
				alert(result.error || 'Error processing request');
			}
		} catch (error) {
			console.error('Error:', error);
			alert('Error processing request');
		}
	}


</script>

<Head
	title="Dashboard - Pending Items"
	description="Manage pending AI tool submissions"
	url="{PUBLIC_SITE_URL}/dashboard"
/>

<div class="container mx-auto px-4 sm:px-6 py-8">
	<div class="mb-8">
		<h1 class="text-3xl md:text-4xl font-bold text-blue-900 mb-6">Dashboard</h1>
		<p class="text-lg text-gray-600">
			Review and manage pending AI tool submissions.
		</p>
	</div>

	{#if data.items.length === 0}
		<Card class="text-center py-12">
			<CardContent>
				<h2 class="text-2xl font-semibold text-gray-600 mb-4">No Pending Items</h2>
				<p class="text-gray-500">All submissions have been reviewed.</p>
			</CardContent>
		</Card>
	{:else}
		<!-- Items List -->
		<div class="space-y-6 mb-12">
			{#each data.items as item}
				<Card class="bg-white border border-gray-200 shadow-sm">
					<CardHeader>
						<div class="flex justify-between items-start">
							<div>
								<CardTitle class="text-xl text-primary-blue">{item.item_name}</CardTitle>
								<p class="text-gray-600 mt-1">{getMetaValue(item, 'description')}</p>
							</div>
							<Badge variant="secondary" class="bg-yellow-100 text-yellow-800">
								Pending
							</Badge>
						</div>
					</CardHeader>
					<CardContent>
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div>
								<h4 class="font-semibold text-gray-700 mb-2">Details</h4>
								<div class="space-y-2 text-sm">
									<div><strong>Website:</strong> 
										<a href={item.item_url} target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline inline-flex items-center gap-1">
											{item.item_url}
											<ExternalLink class="w-3 h-3" />
										</a>
									</div>
									<div><strong>Submitted:</strong> {new Date(item.item_created_at * 1000).toLocaleDateString()}</div>
								</div>
							</div>
							<div>
								<h4 class="font-semibold text-gray-700 mb-2">Description</h4>
								<p class="text-sm text-gray-600 line-clamp-3">{getMetaValue(item, 'content')}</p>
							</div>
						</div>
						
						<div class="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-100">
							<Button 
								variant="outline" 
								onclick={() => handleItemAction(item.item_id, 'reject')}
								class="text-red-600 border-red-300 hover:bg-red-50"
							>
								<X class="w-4 h-4 mr-2" />
								Reject
							</Button>
							<Button 
								onclick={() => handleItemAction(item.item_id, 'approve')}
								class="bg-green-600 hover:bg-green-700 text-white"
							>
								<Check class="w-4 h-4 mr-2" />
								Approve
							</Button>
						</div>
					</CardContent>
				</Card>
			{/each}
		</div>

		<!-- Total Info -->
		<div class="text-center mt-8 text-sm text-gray-600">
			Showing {data.total} pending items total
		</div>
	{/if}
</div>
