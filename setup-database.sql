-- Database setup script for the new schema (one database per site)
-- This script creates all the necessary tables and indexes

-- Drop existing tables if they exist (in correct order due to foreign keys)
DROP TABLE IF EXISTS item_term_relationships CASCADE;
DROP TABLE IF EXISTS item_metas CASCADE;
DROP TABLE IF EXISTS term_metas CASCADE;
DROP TABLE IF EXISTS items CASCADE;
DROP TABLE IF EXISTS terms CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS options CASCADE;

-- Represents a user account.
-- user_type: 0=inactive, 1=member, 2=admin
CREATE TABLE users (
  user_id BIGSERIAL PRIMARY KEY,
  user_firstname TEXT NOT NULL DEFAULT '',
  user_email TEXT NOT NULL UNIQUE,
  user_created_at BIGINT NOT NULL DEFAULT 0,
  user_type INTEGER NOT NULL DEFAULT 1
);

-- Represents a generic item (e.g., a post, product, listing).
-- item_status: 0=pending, 1=active, 2=featured
CREATE TABLE items (
  item_id BIGSERIAL PRIMARY KEY,
  item_name TEXT NOT NULL,
  item_slug TEXT NOT NULL UNIQUE,
  item_url TEXT NOT NULL DEFAULT '',
  item_status INTEGER NOT NULL DEFAULT 0,
  item_created_at BIGINT NOT NULL DEFAULT 0,
  user_id BIGINT NOT NULL,
  
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE RESTRICT
);

CREATE INDEX idx_items_user_id ON items (user_id);
CREATE INDEX idx_items_item_status ON items (item_status);

-- Represents a taxonomy term (e.g., category, tag).
CREATE TABLE terms (
  term_id BIGSERIAL PRIMARY KEY,
  term_name TEXT NOT NULL,
  term_slug TEXT NOT NULL,
  term_taxonomy TEXT NOT NULL,
  
  UNIQUE (term_slug, term_taxonomy)
);

CREATE INDEX idx_terms_term_taxonomy ON terms (term_taxonomy);

-- Junction table linking items to terms.
CREATE TABLE item_term_relationships (
  item_id BIGINT NOT NULL,
  term_id BIGINT NOT NULL,
  
  PRIMARY KEY (item_id, term_id),
  FOREIGN KEY (item_id) REFERENCES items(item_id) ON DELETE CASCADE,
  FOREIGN KEY (term_id) REFERENCES terms(term_id) ON DELETE CASCADE
);

CREATE INDEX idx_item_term_relationships_term_id ON item_term_relationships (term_id, item_id);

-- Metadata for items.
CREATE TABLE item_metas (
  item_meta_id BIGSERIAL PRIMARY KEY,
  item_id BIGINT NOT NULL,
  item_meta_key TEXT NOT NULL,
  item_meta_value TEXT,
  
  UNIQUE (item_id, item_meta_key),
  FOREIGN KEY (item_id) REFERENCES items(item_id) ON DELETE CASCADE
);

CREATE INDEX idx_item_metas_item_id ON item_metas (item_id);

-- Metadata for terms.
CREATE TABLE term_metas (
  term_meta_id BIGSERIAL PRIMARY KEY,
  term_id BIGINT NOT NULL,
  term_meta_key TEXT NOT NULL,
  term_meta_value TEXT,
  
  UNIQUE (term_id, term_meta_key),
  FOREIGN KEY (term_id) REFERENCES terms(term_id) ON DELETE CASCADE
);

CREATE INDEX idx_term_metas_term_id ON term_metas (term_id);

-- System-wide options or settings.
CREATE TABLE options (
  option_id BIGSERIAL PRIMARY KEY,
  option_key TEXT NOT NULL UNIQUE,
  option_value TEXT
);

-- Insert some default data
INSERT INTO users (user_firstname, user_email, user_created_at, user_type) VALUES 
('Admin', '<EMAIL>', EXTRACT(EPOCH FROM NOW())::BIGINT, 2);

-- Insert default categories
INSERT INTO terms (term_name, term_slug, term_taxonomy) VALUES 
('AI Tools', 'ai-tools', 'category'),
('Productivity', 'productivity', 'category'),
('Design', 'design', 'category'),
('Development', 'development', 'category'),
('Marketing', 'marketing', 'category');

-- Insert default pricing options
INSERT INTO terms (term_name, term_slug, term_taxonomy) VALUES 
('Free', 'free', 'pricing'),
('Freemium', 'freemium', 'pricing'),
('Paid', 'paid', 'pricing');

-- Insert default options
INSERT INTO options (option_key, option_value) VALUES 
('site_title', 'Acid Tools'),
('site_description', 'Discover the best AI tools and productivity software'),
('items_per_page', '40');
