import { json, redirect } from '@sveltejs/kit';
import type { <PERSON>questHand<PERSON> } from './$types';
import { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, PUBLIC_SITE_URL } from '$lib/utils/env.js';
import { getUserByEmail, saveUser } from '$lib/services/users.js';
import { generateUserToken } from '$lib/services/auth.js';

interface GoogleTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  id_token: string;
}

interface GoogleUserInfo {
  id: string;
  email: string;
  verified_email: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
}

export const GET: RequestHandler = async ({ url, cookies }) => {
  try {
    const code = url.searchParams.get('code');
    const state = url.searchParams.get('state') || '/';
    
    if (!code) {
      throw redirect(302, `/login?error=missing_code&redirectTo=${encodeURIComponent(state)}`);
    }

    // Exchange code for access token
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: GOOGLE_CLIENT_ID,
        client_secret: GOOGLE_CLIENT_SECRET,
        code,
        grant_type: 'authorization_code',
        redirect_uri: `${PUBLIC_SITE_URL}/api/auth/google/callback`,
      }),
    });

    if (!tokenResponse.ok) {
      console.error('Token exchange failed:', await tokenResponse.text());
      throw redirect(302, `/login?error=token_exchange_failed&redirectTo=${encodeURIComponent(state)}`);
    }

    const tokenData: GoogleTokenResponse = await tokenResponse.json();

    // Get user info from Google
    const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
      headers: {
        Authorization: `Bearer ${tokenData.access_token}`,
      },
    });

    if (!userResponse.ok) {
      console.error('User info fetch failed:', await userResponse.text());
      throw redirect(302, `/login?error=user_info_failed&redirectTo=${encodeURIComponent(state)}`);
    }

    const googleUser: GoogleUserInfo = await userResponse.json();

    if (!googleUser.verified_email) {
      throw redirect(302, `/login?error=email_not_verified&redirectTo=${encodeURIComponent(state)}`);
    }

    // Check if user exists or create new user
    let user = await getUserByEmail(googleUser.email);
    
    if (!user) {
      // Create new user
      user = await saveUser({
        user_firstname: googleUser.given_name || googleUser.name,
        user_email: googleUser.email.toLowerCase(),
        user_created_at: Date.now(),
        user_type: 1 // Default to member
      });
    }

    // Check if user is active
    if (user.user_type === 0) {
      throw redirect(302, `/login?error=account_inactive&redirectTo=${encodeURIComponent(state)}`);
    }

    // Generate JWT token
    const tokenPayload = {
      user_id: user.user_id!,
      user_email: user.user_email,
      user_firstname: user.user_firstname,
      user_type: user.user_type,
      type: 'user' as const
    };

    const token = generateUserToken(tokenPayload);

    // Set auth cookie
    cookies.set('auth_token', token, {
      path: '/',
      httpOnly: true,
      secure: false,
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    });

    // Redirect to original destination
    throw redirect(302, state);
    
  } catch (error) {
    if (error instanceof Response) {
      throw error;
    }
    
    console.error('Google OAuth callback error:', error);
    throw redirect(302, '/login?error=authentication_failed');
  }
};
