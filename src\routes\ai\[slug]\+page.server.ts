import { getItemBySlug, getItemsWithBasicMetadata, getActiveItemsByTermWithMetadata } from '$lib/services/items.js';
import { getItemMetas } from '$lib/services/item_metas.js';
import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import sql from '$lib/db/db.js';

export const load: PageServerLoad = async ({ params }) => {
	try {
		const slug = params.slug;

		// Get the item by slug
		const item = await getItemBySlug(slug);
		
		if (!item) {
			throw error(404, 'Tool not found');
		}
		
		// Get item metadata
		const { item_metas } = await getItemMetas(item.item_id, 100, 0);

		// Convert metadata to object for easier access
		const metadata: Record<string, string> = {};
		item_metas.forEach(meta => {
			metadata[meta.item_meta_key] = meta.item_meta_value || '';
		});

		// Get term relationships for this item
		const termRelationships = await sql`
			SELECT
				t.term_id,
				t.term_name,
				t.term_slug,
				t.term_taxonomy
			FROM item_term_relationships itr
			JOIN terms t ON itr.term_id = t.term_id
			WHERE itr.item_id = ${item.item_id}
		`;

		// Get related tools from the same category
		let relatedItems: any[] = [];
		let firstCategorySlug = '';

		// Find the first category of this item
		const firstCategory = termRelationships.find((t: any) => t.term_taxonomy === 'category');
		if (firstCategory) {
			firstCategorySlug = firstCategory.term_slug;
			const { items } = await getActiveItemsByTermWithMetadata(firstCategory.term_slug, 'category', 8, 0);
			relatedItems = items.filter(i => i.item_id !== item.item_id).slice(0, 4);
		}

		// If no related items from category, get random active items
		if (relatedItems.length === 0) {
			const { items } = await getItemsWithBasicMetadata(4, 0);
			relatedItems = items.filter(i => i.item_id !== item.item_id).slice(0, 4);
		}

		return {
			item,
			metadata,
			terms: termRelationships,
			relatedItems,
			firstCategorySlug
		};
	} catch (err) {
		if (err instanceof Error && 'status' in err) {
			throw err;
		}
		console.error('Error loading tool data:', err);
		throw error(500, 'Failed to load tool data');
	}
};
