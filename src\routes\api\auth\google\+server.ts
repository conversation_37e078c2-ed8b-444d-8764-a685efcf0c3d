import { json, redirect } from '@sveltejs/kit';
import type { <PERSON>questHand<PERSON> } from './$types';
import { GOOGLE_CLIENT_ID, PUBLIC_SITE_URL } from '$lib/utils/env.js';

export const GET: RequestHandler = async ({ url }) => {
  try {
    const redirectTo = url.searchParams.get('redirectTo') || '/';
    
    const googleAuthUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth');
    googleAuthUrl.searchParams.set('client_id', GOOGLE_CLIENT_ID);
    googleAuthUrl.searchParams.set('redirect_uri', `${PUBLIC_SITE_URL}/api/auth/google/callback`);
    googleAuthUrl.searchParams.set('response_type', 'code');
    googleAuthUrl.searchParams.set('scope', 'openid email profile');
    googleAuthUrl.searchParams.set('state', redirectTo);
    
    throw redirect(302, googleAuthUrl.toString());
  } catch (error) {
    if (error instanceof Response) {
      throw error;
    }
    
    return json({
      success: false,
      error: 'Failed to initiate Google authentication'
    }, { status: 500 });
  }
};
