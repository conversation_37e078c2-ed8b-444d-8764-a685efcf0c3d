import sql from '$lib/db/db.js';
import type { ItemMeta } from '$lib/types/tables.js';

export async function saveItemMeta(itemMeta: ItemMeta): Promise<ItemMeta> {
  if (itemMeta.item_meta_id) {
    const [updatedItemMeta] = await sql`
      UPDATE item_metas
      SET item_id = ${itemMeta.item_id},
          item_meta_key = ${itemMeta.item_meta_key},
          item_meta_value = ${itemMeta.item_meta_value || null}
      WHERE item_meta_id = ${itemMeta.item_meta_id}
      RETURNING *
    `;
    return updatedItemMeta as ItemMeta;
  } else {
    const [newItemMeta] = await sql`
      INSERT INTO item_metas (item_id, item_meta_key, item_meta_value)
      VALUES (${itemMeta.item_id}, ${itemMeta.item_meta_key}, ${itemMeta.item_meta_value || null})
      RETURNING *
    `;
    return newItemMeta as ItemMeta;
  }
}

export async function deleteItemMeta(item_meta_id: number): Promise<boolean> {
  const result = await sql`
    DELETE FROM item_metas WHERE item_meta_id = ${item_meta_id}
  `;
  return result.count > 0;
}

export async function getItemMetas(item_id?: number, limit = 50, offset = 0): Promise<{ item_metas: ItemMeta[]; total: number }> {
  let itemMetas;
  let countResult;

  if (item_id !== undefined) {
    itemMetas = await sql`
      SELECT * FROM item_metas
      WHERE item_id = ${item_id}
      ORDER BY item_meta_key
      LIMIT ${limit} OFFSET ${offset}
    `;

    [countResult] = await sql`
      SELECT COUNT(*) as count FROM item_metas WHERE item_id = ${item_id}
    `;
  } else {
    itemMetas = await sql`
      SELECT * FROM item_metas
      ORDER BY item_meta_key
      LIMIT ${limit} OFFSET ${offset}
    `;

    [countResult] = await sql`
      SELECT COUNT(*) as count FROM item_metas
    `;
  }

  return { item_metas: itemMetas as unknown as ItemMeta[], total: Number(countResult.count) };
}

export async function getItemMetaById(item_meta_id: number): Promise<ItemMeta | null> {
  const [itemMeta] = await sql`
    SELECT * FROM item_metas WHERE item_meta_id = ${item_meta_id}
  `;
  return (itemMeta as ItemMeta) || null;
}