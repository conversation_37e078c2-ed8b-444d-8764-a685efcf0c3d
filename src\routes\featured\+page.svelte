<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import ToolCard from '$lib/components/ToolCard.svelte';
	import Head from '$lib/components/Head.svelte';
	import { ChevronLeft, ChevronRight, Star } from 'lucide-svelte';
	import { PUBLIC_SITE_NAME, PUBLIC_SITE_URL } from '$lib/utils/env';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	// Helper function to get metadata value
	function getMetaValue(item: any, key: string): string {
		if (!item.metadata) return '';
		const meta = item.metadata.find((m: any) => m.item_meta_key === key);
		return meta ? meta.item_meta_value || '' : '';
	}

	// Helper function to get term color (default for now)
	function getTermColor(item: any, taxonomy: string): string {
		// For now return default color, will enhance with term_color metadata later
		return '#3730a3';
	}

	// Helper function to get category name
	function getCategoryName(item: any): string {
		if (!item.terms) return '';
		const categoryTerm = item.terms.find((t: any) => t.term_taxonomy === 'category');
		return categoryTerm?.term_name || '';
	}

	// Helper function to get pricing name
	function getPricingName(item: any): string {
		if (!item.terms) return '';
		const pricingTerm = item.terms.find((t: any) => t.term_taxonomy === 'pricing');
		return pricingTerm?.term_name || '';
	}

	// Helper function to get category slug
	function getCategorySlug(item: any): string {
		if (!item.terms) return '';
		const categoryTerm = item.terms.find((t: any) => t.term_taxonomy === 'category');
		return categoryTerm?.term_slug || '';
	}

	// Helper function to get pricing slug
	function getPricingSlug(item: any): string {
		if (!item.terms) return '';
		const pricingTerm = item.terms.find((t: any) => t.term_taxonomy === 'pricing');
		return pricingTerm?.term_slug || '';
	}

	// Helper function to get thumbnail URL
	function getThumbnailUrl(item: any): string {
		if (!item.metadata) return '/assets/images/default-screenshot.jpg';
		const meta = item.metadata.find((m: any) => m.item_meta_key === 'thumbnail_url');
		return meta && meta.item_meta_value ? meta.item_meta_value : '/assets/images/default-screenshot.jpg';
	}
</script>

<Head
	title="Featured AI Tools - {PUBLIC_SITE_NAME}"
	description="Discover the best featured AI tools handpicked by our team."
	url="{PUBLIC_SITE_URL}/featured"
/>

<div class="container mx-auto py-12 px-4 sm:px-6">
	<div class="text-center mb-12">
		<div class="flex items-center justify-center mb-6">
			<Star class="w-8 h-8 text-orange-500 mr-3 fill-current" />
			<h1 class="text-3xl md:text-4xl font-bold text-blue-900">
				Featured AI Tools
			</h1>
		</div>
		<p class="text-lg text-gray-600 max-w-2xl mx-auto">
			Discover the best AI tools handpicked by our team. These featured tools represent the cutting edge of AI innovation.
		</p>
	</div>

	<!-- Tools Grid -->
	{#if data.items.length > 0}
		<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
			{#each data.items as item}
				<ToolCard
					name={item.item_name}
					description={getMetaValue(item, 'description') || 'AI tool for productivity'}
					category={getCategoryName(item)}
					price={getPricingName(item)}
					imageColor="e0e7ff/3730a3"
					categoryColor={getTermColor(item, 'category')}
					slug={item.item_slug}
					thumbnailUrl={getThumbnailUrl(item)}
					categorySlug={getCategorySlug(item)}
					pricingSlug={getPricingSlug(item)}
				/>
			{/each}
		</div>

		<!-- Pagination -->
		{#if data.totalPages > 1}
			<div class="flex justify-center items-center gap-4 mt-12">
				{#if data.hasPrevPage}
					<Button href="/featured?page={data.currentPage - 1}" variant="outline" class="flex items-center gap-2">
						<ChevronLeft class="w-4 h-4" />
						Previous
					</Button>
				{/if}
				
				<span class="text-gray-600">
					Page {data.currentPage} of {data.totalPages}
				</span>
				
				{#if data.hasNextPage}
					<Button href="/featured?page={data.currentPage + 1}" variant="outline" class="flex items-center gap-2">
						Next
						<ChevronRight class="w-4 h-4" />
					</Button>
				{/if}
			</div>
		{/if}
	{:else}
		<div class="text-center py-16">
			<Star class="w-16 h-16 text-gray-300 mx-auto mb-4" />
			<h3 class="text-xl font-semibold text-gray-600 mb-2">No Featured Tools Yet</h3>
			<p class="text-gray-500">Check back soon for our handpicked selection of the best AI tools.</p>
		</div>
	{/if}
</div>
